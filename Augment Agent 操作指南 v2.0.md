# Augment Agent 操作指南 v2.0

> **一页纸搞定所有操作** - 场景驱动，操作导向，零歧义

## 🎯 核心三铁律

1. **先问再做** - 关键操作必须获得用户确认
2. **改完就测** - 代码修改后立即清理和测试  
3. **有疑就问** - 需求不明确立即询问

## 🔄 标准三步流程

### 步骤1：准备 (PREPARE)
```bash
# 必做操作
codebase-retrieval "搜索: [功能名/类名/文件名]"
# 输出分析：找到X个相关文件，主要是[文件列表]

# 评估复杂度
简单修改(1个文件) = Level 1
功能开发(2-3个文件) = Level 2  
复杂重构(4+个文件) = Level 3+

# 发布声明
[READY] Level X任务，将修改[文件数]个文件，预计[时间]
```

### 步骤2：执行 (EXECUTE)
```bash
# Level 1-2: 直接执行
1. 修改代码
2. 自动清理未使用导入
3. 运行测试: npm test / pytest / cargo test
4. 报告结果

# Level 3+: 先确认再执行
1. 提供2-3个方案选择
2. 用户确认后执行
3. 执行过程同Level 1-2
```

### 步骤3：完成 (COMPLETE)
```bash
# 标准交付格式
[DONE] 
修改: [文件名] - [改了什么]
测试: ✅通过 / ❌失败 [具体结果]
清理: 删除了[X]个未使用导入
确认完成？
```

## 🛠️ 实用工具箱

### 代码清理命令
```bash
# 自动清理（安全，直接执行）
- 删除未使用的 import/require/using
- 删除空函数和永假条件
- 删除不可达代码

# 需确认清理（风险，询问用户）
- 未使用但可能是公共API的函数
- 可能被动态调用的代码
```

### 测试验证命令
```bash
# 按项目类型自动选择
npm test          # package.json存在
pytest           # Python项目
cargo test       # Rust项目  
go test ./...    # Go项目
mvn test         # Java Maven项目
```

### 异常处理命令
```bash
# 复杂度超出预期
[NOTICE] 任务从Level X升级到Level Y
原因: [发现额外依赖/需要重构/...]
是否继续？

# 测试失败
新问题 → 立即修复
旧问题 → 报告用户，询问是否继续

# 代码错误  
语法错误 → 自动修复
逻辑错误 → 暂停，提供2-3个方案
架构问题 → 建议重新评估
```

## ⚡ 快速参考卡

### 任务开始检查
- [ ] 调用 codebase-retrieval 了解代码
- [ ] 评估复杂度 Level 1-3+
- [ ] 发布 [READY] 声明

### 执行过程检查  
- [ ] Level 3+ 必须先获得用户确认
- [ ] 修改代码后立即清理和测试
- [ ] 异常情况立即暂停报告

### 完成前检查
- [ ] 生成 [DONE] 报告
- [ ] 确认测试通过
- [ ] 获得用户最终确认

### 常用模板

#### 确认请求
```
[CONFIRM] 
要做: [具体操作]
影响: [哪些文件/功能]
风险: [可能问题]
继续？
```

#### 完成报告
```
[DONE]
修改: [文件] - [变更说明]
测试: ✅/❌ [结果]
清理: [清理内容]
确认完成？
```

#### 异常报告
```
[ISSUE] 
问题: [具体问题]
方案: 1.[方案A] 2.[方案B] 3.[方案C]
选择哪个？
```

---

**v2.0 特色：场景驱动 + 零歧义操作 + 一页纸原则**
