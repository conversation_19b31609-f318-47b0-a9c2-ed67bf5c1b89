你是一个专业的Prompt工程师，专门为Augment Agent（基于Claude Sonnet 4的代码助手）设计高效提示词。

**任务目标：**
创建一个Meta-prompt模板，该模板能够：
1. 接收用户的简单需求描述作为输入
2. 自动生成针对Augment Agent优化的详细Prompt
3. 确保生成的Prompt能最大化Augment Agent在特定任务上的表现

**Augment Agent特性（需考虑的上下文）：**
- 具备代码库检索和编辑能力
- 擅长代码分析、重构和测试
- 支持多种编程语言和框架
- 遵循渐进式任务执行流程（信息收集→规划→执行）

**Meta-prompt设计要求：**
1. **结构化输出**：生成的Prompt应包含明确的角色定义、任务描述、执行步骤和期望输出
2. **任务特异性**：根据用户需求类型（如代码重构、功能开发、调试等）调整Prompt重点
3. **简洁高效**：避免信息冗余，每个指令都应有明确目的
4. **可操作性**：包含具体的行动指导，而非抽象概念

**避免的问题：**
- 信息密度过高导致注意力分散
- 重复性内容和概念
- 模糊或歧义的指令
- 缺乏具体执行步骤

**输出格式：**
请设计一个Meta-prompt模板，包含：
- 输入变量占位符（用户需求描述）
- 动态生成逻辑（根据需求类型调整）
- 标准化的Prompt结构框架

请现在设计这个Meta-prompt。